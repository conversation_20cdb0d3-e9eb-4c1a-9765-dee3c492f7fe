#!/bin/bash

# Simple script to extract photos from AWS EC2 bimbo-hunter instance
# This script uses rsync to safely copy all user images

# Configuration - UPDATE THESE VALUES
AWS_HOST="your-ec2-instance-ip-or-hostname"
AWS_USER="ec2-user"  # or ubuntu, depending on your AMI
AWS_KEY_PATH="~/.ssh/your-key.pem"  # Path to your SSH key
REMOTE_PROJECT_PATH="/path/to/bimbo-hunter-base"  # Update this path
LOCAL_EXTRACT_DIR="./extracted_photos"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo "=== Bimbo Hunter Photo Extraction Tool ==="
echo "This script will safely copy all user-submitted photos from your AWS EC2 instance"
echo ""

# Check if configuration is updated
if [ "$AWS_HOST" = "your-ec2-instance-ip-or-hostname" ]; then
    echo -e "${RED}ERROR: Please update the configuration variables at the top of this script!${NC}"
    echo "You need to set:"
    echo "  - AWS_HOST (your EC2 instance IP or hostname)"
    echo "  - AWS_USER (usually 'ec2-user' or 'ubuntu')"
    echo "  - AWS_KEY_PATH (path to your SSH private key)"
    echo "  - REMOTE_PROJECT_PATH (path to bimbo-hunter-base on EC2)"
    exit 1
fi

echo "Configuration:"
echo "  AWS Host: $AWS_HOST"
echo "  AWS User: $AWS_USER"
echo "  SSH Key: $AWS_KEY_PATH"
echo "  Remote Path: $REMOTE_PROJECT_PATH"
echo "  Local Directory: $LOCAL_EXTRACT_DIR"
echo ""

# Create local directory
echo -e "${YELLOW}Creating local directory...${NC}"
mkdir -p "$LOCAL_EXTRACT_DIR"

# Test SSH connection
echo -e "${YELLOW}Testing SSH connection...${NC}"
if ssh -i "$AWS_KEY_PATH" -o ConnectTimeout=10 "$AWS_USER@$AWS_HOST" "echo 'Connection successful'" 2>/dev/null; then
    echo -e "${GREEN}✅ SSH connection successful${NC}"
else
    echo -e "${RED}❌ SSH connection failed${NC}"
    echo "Please check your AWS_HOST, AWS_USER, and AWS_KEY_PATH settings"
    exit 1
fi

# Check if remote directory exists
echo -e "${YELLOW}Checking remote directory...${NC}"
if ssh -i "$AWS_KEY_PATH" "$AWS_USER@$AWS_HOST" "[ -d '$REMOTE_PROJECT_PATH/client/public/user-images' ]" 2>/dev/null; then
    echo -e "${GREEN}✅ Remote user-images directory found${NC}"
else
    echo -e "${RED}❌ Remote user-images directory not found${NC}"
    echo "Please check your REMOTE_PROJECT_PATH setting"
    echo "Looking for: $REMOTE_PROJECT_PATH/client/public/user-images"
    exit 1
fi

# Count files to be copied
echo -e "${YELLOW}Counting files to copy...${NC}"
file_count=$(ssh -i "$AWS_KEY_PATH" "$AWS_USER@$AWS_HOST" "find '$REMOTE_PROJECT_PATH/client/public/user-images' -type f | wc -l" 2>/dev/null)
if [ "$file_count" -gt 0 ]; then
    echo -e "${GREEN}Found $file_count files to copy${NC}"
else
    echo -e "${YELLOW}No files found in user-images directory${NC}"
    echo "This might be normal if no users have uploaded photos yet."
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 0
    fi
fi

# Perform the copy
echo -e "${YELLOW}Starting rsync copy...${NC}"
echo "This may take a while depending on the number and size of images."
echo ""

rsync -avz --progress \
    -e "ssh -i $AWS_KEY_PATH" \
    "$AWS_USER@$AWS_HOST:$REMOTE_PROJECT_PATH/client/public/user-images/" \
    "$LOCAL_EXTRACT_DIR/user-images/"

if [ $? -eq 0 ]; then
    echo ""
    echo -e "${GREEN}✅ Photo extraction completed successfully!${NC}"
    echo "Photos are now available in: $LOCAL_EXTRACT_DIR/user-images/"
    
    # Show summary
    local_count=$(find "$LOCAL_EXTRACT_DIR/user-images" -type f 2>/dev/null | wc -l)
    echo "Total files copied: $local_count"
    
    # Show directory structure
    echo ""
    echo "Directory structure:"
    ls -la "$LOCAL_EXTRACT_DIR/user-images/" 2>/dev/null || echo "No files copied"
    
else
    echo ""
    echo -e "${RED}❌ Photo extraction failed!${NC}"
    echo "Check the error messages above for details."
    exit 1
fi
