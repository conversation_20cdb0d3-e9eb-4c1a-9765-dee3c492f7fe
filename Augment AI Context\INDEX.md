# Augment AI Context Documentation

This folder contains documentation files created during the development of the Bimbo Hunter project. These files serve as reference material for understanding the project's features, setup procedures, and implementation details.

## Purpose

These documentation files are primarily for human reference and are not actively used by the AI system for context during development. They contain:

- Setup guides and installation instructions
- Feature explanations and implementation details
- Troubleshooting information
- Configuration examples
- Testing procedures

## File Contents

### Main Application Documentation
- **ADMIN_COMMANDS.md** - Guide for administrative commands and database management
- **PRODUCTION-DEPLOYMENT.md** - Production deployment procedures and configuration



## Usage

These files can be referenced when:
- Setting up the project for the first time
- Troubleshooting issues
- Understanding feature implementations
- Deploying to production

## Note

If you need to reference any of these documents during development, they are available in this folder. The AI assistant does not automatically use these files for context but can access them if specifically requested.
