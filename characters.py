"""
Characters data module.

This file contains a dictionary of all characters organized from the portrait images.
Each character entry includes:
- rarity: The character's rarity (FREE, R, SR, SSR, UR+)
- Name: The character's name
- Source: The source media the character is from
- Portrait: Path to the character's portrait image
- Thumbnail: Path to the character's thumbnail image
- description: Description of the character's appearance
- conditions: Special conditions for claiming this character
"""

characters = [
    # FREE characters
    {
        "rarity": "FREE",
        "Name": "Frieren",
        "Source": "Sousou no Frieren",
        "Portrait": "/portraits/FREE - Frieren - Sousou no Frieren - portrait.png",
        "Thumbnail": "/thumbnails/Frieren.png",
        "description": "Elf ears, white hair, white outfit, staff",
        "conditions": "None",
    },
    {
        "rarity": "FREE",
        "Name": "<PERSON><PERSON>",
        "Source": "Dandadan",
        "Portrait": "/portraits/FREE - <PERSON><PERSON> - <PERSON> - portrait.png",
        "Thumbnail": "/thumbnails/Momo <PERSON>.png",
        "description": "Brown hair, pink sweater, high socks",
        "conditions": "None",
    },

    # R characters
    {
        "rarity": "R",
        "Name": "<PERSON>gatsu<PERSON> Zenitsu",
        "Source": "<PERSON>etsu no Yaiba",
        "Portrait": "/portraits/R - Agatsuma Zenitsu - Kimetsu no Yaiba - Portrait.png",
        "Thumbnail": "/thumbnails/Agatsuma Zenitsu.png",
        "description": "Orange haori, yellow wig, black uniform",
        "conditions": "You must ask the cosplay to pretend to be asleep.",
    },
    {
        "rarity": "R",
        "Name": "Anges Tachyon",
        "Source": "Umamusume",
        "Portrait": "/portraits/R - Anges Tachyon -  Umamusume - portrait.png",
        "Thumbnail": "/thumbnails/Anges Tachyon.png",
        "description": "Brown hair, labcoat, horse ears, tail",
        "conditions": "None",
    },
    {
        "rarity": "R",
        "Name": "Anya Forger",
        "Source": "Spy x Family",
        "Portrait": "/portraits/R - Anya Forger - Spy x Family - portrait.png",
        "Thumbnail": "/thumbnails/Anya Forger.png",
        "description": "Pink hair, black dress, hair accessories",
        "conditions": "Must be cosplayed by a man, perferably muscular.",
    },
    {
        "rarity": "R",
        "Name": "Emilia",
        "Source": "Re Zero",
        "Portrait": "/portraits/R - Emilia - Re Zero - portrait.png",
        "Thumbnail": "/thumbnails/Emilia.png",
        "description": "White hair, white dress, elf ears",
        "conditions": "None",
    },
    {
        "rarity": "R",
        "Name": "Esil",
        "Source": "Solo Leveling",
        "Portrait": "/portraits/R - Esil - Solo Leveling - portrait.png",
        "Thumbnail": "/thumbnails/Esil.png",
        "description": "Purple hair, red face markings, black outfit",
        "conditions": "None",
    },
    {
        "rarity": "R",
        "Name": "Fami",
        "Source": "Chainsaw Man",
        "Portrait": "/portraits/R - Fami - Chainsaw Man - portrait.png",
        "Thumbnail": "/thumbnails/Fami.png",
        "description": "White hair, beauty marks, school uniform",
        "conditions": "None",
    },
    {
        "rarity": "R",
        "Name": "Jinshi & Maomao",
        "Source": "The Apothecary Diaries",
        "Portrait": "/portraits/R - Jinshi & Maomao - The Apothecary Diaries - portrait.png",
        "Thumbnail": "/thumbnails/Jinshi & Maomao.png",
        "description": "traditional chinese outfit, long hair, male/female couple",
        "conditions": "Duo - Both characters must be in the photo",
    },
    {
        "rarity": "R",
        "Name": "Jane Doe",
        "Source": "Zenless Zone Zero",
        "Portrait": "/portraits/R - Jane Doe - Zenless Zone Zero - portrait.png",
        "Thumbnail": "/thumbnails/Jane Doe.png",
        "description": "Jean Jacket, mouse ears, red and black hair, ripped tights.",
        "conditions": "None",
    },
    {
        "rarity": "R",
        "Name": "Joseph Joestar",
        "Source": "JoJo's Bizarre Adventure",
        "Portrait": "/portraits/R - Joseph Joestar - JoJo's Bizarre Adventure - portrait.png",
        "Thumbnail": "/thumbnails/Joseph Joestar.png",
        "description": "Brown hat, brown shirt, older man,",
        "conditions": "None",
    },
    {
        "rarity": "R",
        "Name": "Kazuho Haneyama",
        "Source": "MHA Vigalantes",
        "Portrait": "/portraits/R - Kazuho Haneyama- MHA Vigalantes - portrait.png",
        "Thumbnail": "/thumbnails/Kazuho Haneyama.png",
        "description": "Pink pigtails, black outfit, lower back wings",
        "conditions": "None",
    },
    {
        "rarity": "R",
        "Name": "Nanakusa Nazuna",
        "Source": "Call of the Night",
        "Portrait": "/portraits/R - Nanakusa Nazuna- Call of the Night - portrait.png",
        "Thumbnail": "/thumbnails/Nanakusa Nazuna.png",
        "description": "braids, black outfit, vampire teeth",
        "conditions": "None",
    },
    {
        "rarity": "R",
        "Name": "Narumi Gen",
        "Source": "Kaiju no. 8",
        "Portrait": "/portraits/R - Narumi Gen - Kaiju no. 8 - portrait.png",
        "Thumbnail": "/thumbnails/Narumi Gen.png",
        "description": "combat uniform, pink hair, pink eyebrows",
        "conditions": "None",
    },
    {
        "rarity": "R",
        "Name": "Nichole and Jecka",
        "Source": "Class of '09",
        "Portrait": "/portraits/R - Nichole and Jecka - Class of '09 - portrait.png",
        "Thumbnail": "/thumbnails/Nichole and Jecka.png",
        "description": "blonde girl with pink shirt, brunette girl with blue shirt",
        "conditions": "Duo - Both characters must be in the photo",
    },
    {
        "rarity": "R",
        "Name": "Orihime Inoue",
        "Source": "Bleach",
        "Portrait": "/portraits/R - Orihime Inoue - Bleach - portrait.png",
        "Thumbnail": "/thumbnails/Orihime Inoue.png",
        "description": "Orange hair, white dress or school uniform with yellow vest",
        "conditions": "None",
    },
    {
        "rarity": "R",
        "Name": "Reze",
        "Source": "Chainsaw Man",
        "Portrait": "/portraits/R - Reze - Chainsaw Man - portrait.png",
        "Thumbnail": "/thumbnails/Reze.png",
        "description": "Purple hair, choker, white shirt, black pants",
        "conditions": "None",
    },
    {
        "rarity": "R",
        "Name": "Sebastian Michaelis",
        "Source": "Black Butler",
        "Portrait": "/portraits/R - Sebastian Michaelis - Black Butler - portrait.png",
        "Thumbnail": "/thumbnails/Sebastian Michaelis.png",
        "description": "black hair, butler outfit, glasses",
        "conditions": "None",
    },
    {
        "rarity": "R",
        "Name": "Seiko Ayase",
        "Source": "Dandadan",
        "Portrait": "/portraits/R - Seiko Ayase -  Dandadan - portrait.png",
        "Thumbnail": "/thumbnails/Seiko Ayase.png",
        "description": "white tall hair, bomber jacket, glasses, no pants",
        "conditions": "None",
    },
    {
        "rarity": "R",
        "Name": "Shadow the Hedgehog",
        "Source": "Sonic",
        "Portrait": "/portraits/R - Shadow the Hedgehog - Sonic - portrait.png",
        "Thumbnail": "/thumbnails/Shadow the Hedgehog.png",
        "description": "Black body suit, white gloves, spikey black and red hair",
        "conditions": "None",
    },
    {
        "rarity": "R",
        "Name": "Shikanoko Noko",
        "Source": "Shikanoko Nokonoko Koshitantan",
        "Portrait": "/portraits/R - Shikanoko Noko - Shikanoko Nokonoko Koshitantan - portrait.png",
        "Thumbnail": "/thumbnails/Shikanoko Noko.png",
        "description": "School uniform, deer antlers",
        "conditions": "None",
    },
    {
        "rarity": "R",
        "Name": "Shinmon Benimaru",
        "Source": "Fire Force",
        "Portrait": "/portraits/R - Shinmon Benimaru- Fire Force - portrait.png",
        "Thumbnail": "/thumbnails/Shinmon Benimaru.png",
        "description": "shoulder length hair, navy blue haori, one sleeve",
        "conditions": "None",
    },
    {
        "rarity": "R",
        "Name": "Suou Tamaki",
        "Source": "Ouran Highschool Host Club",
        "Portrait": "/portraits/R - Suou Tamaki - Ouran Highschool Host Club - portrait.png",
        "Thumbnail": "/thumbnails/Suou Tamaki.png",
        "description": "blonde hair, blue suit, school uniform,",
        "conditions": "None",
    },
    {
        "rarity": "R",
        "Name": "Sylus",
        "Source": "Love in Deep Space",
        "Portrait": "/portraits/R - Sylus - Love in Deep Space - portrait.png",
        "Thumbnail": "/thumbnails/Sylus.png",
        "description": "white hair, red eyes, chest strap, dark clothes",   
        "conditions": "None",
    },
    {
        "rarity": "R",
        "Name": "Tsunade",
        "Source": "Naruto Shippuden",
        "Portrait": "/portraits/R - Tsunade - Naruto Shippuden - portrait.png",
        "Thumbnail": "/thumbnails/Tsunade.png",
        "description": "blonde hair, green jacket, just big ol' titties",
        "conditions": "None",
    },

    # SR characters
    {
        "rarity": "SR",
        "Name": "Alex Louis Armstrong",
        "Source": "Full Metal Alchemist Brotherhood",
        "Portrait": "/portraits/SR - Alex Louis Armstrong - Full Metal Alchemist Brotherhood - portrait.png",
        "Thumbnail": "/thumbnails/Alex Louis Armstrong.png",
        "description": "Bald except blonde ahoge, probably shirtless, military uniform",
        "conditions": "None",
    },
    {
        "rarity": "SR",
        "Name": "Asakura Shin & Lu Shaotang",
        "Source": "Sakamoto Days",
        "Portrait": "/portraits/SR - Asakura Shin & Lu Shaotang - Sakamoto Days - portrait.png",
        "Thumbnail": "/thumbnails/Asakura Shin & Lu Shaotang.png",
        "description": "Blonde hair boy, pink hair girl with braid, green aprons, near a Sakamoto",
        "conditions": "Duo - Both characters must be in the photo",
    },
    {
        "rarity": "SR",
        "Name": "Brazilian Miku",
        "Source": "Meme",
        "Portrait": "/portraits/SR - Brazilian Miku - Meme - portrait.png",
        "Thumbnail": "/thumbnails/Brazilian Miku.png",
        "description": "Yellow crop top, jean shorts, teal pigtails,",
        "conditions": "None",
    },
    {
        "rarity": "SR",
        "Name": "Gemma",
        "Source": "Monster Hunter Wilds",
        "Portrait": "/portraits/SR - Gemma - Monster Hunter Wilds - portrait.png",
        "Thumbnail": "/thumbnails/Gemma.png",
        "description": "Huge blonde hair, showing off stomach, blacksmith outfit",
        "conditions": "None",
    },
    {
        "rarity": "SR",
        "Name": "Hayato Suou",
        "Source": "Windbreaker",
        "Portrait": "/portraits/SR - Hayato Suou - Windbreaker - portrait.png",
        "Thumbnail": "/thumbnails/Hayato Suou.png",
        "description": "Eye patch, feathered earings, black jacket with green collar",
        "conditions": "None",
    },
    {
        "rarity": "SR",
        "Name": "Kabru",
        "Source": "Dungeon Meshi",
        "Portrait": "/portraits/SR - Kabru - Dungeon Meshi - portrait.png",
        "Thumbnail": "/thumbnails/Kabru.png",
        "description": "Armor, dark skin, black hair, blue eyes",
        "conditions": "None",
    },
    {
        "rarity": "SR",
        "Name": "Khun Aguero Agnis",
        "Source": "Tower of God",
        "Portrait": "/portraits/SR - Khun Aguero Agnis - Tower of God - portrait.png",
        "Thumbnail": "/thumbnails/Khun Aguero Agnis.png",
        "description": "Blue hair, white shirt, black tie, suitcase",   
        "conditions": "None",
    },
    {
        "rarity": "SR",
        "Name": "Nice",
        "Source": "To Be Hero X",
        "Portrait": "/portraits/SR - Nice - To Be Hero X - portrait..png",
        "Thumbnail": "/thumbnails/Nice.png",
        "description": "White hair, white hero outfit, cape",
        "conditions": "None",
    },
    {
        "rarity": "SR",
        "Name": "Nicholas D. Wolfwood",
        "Source": "Trigun",
        "Portrait": "/portraits/SR - Nicholas D. Wolfwood - Trigun - portrait.png",
        "Thumbnail": "/thumbnails/Nicholas D. Wolfwood.png",
        "description": "black suit, carrying large cross",
        "conditions": "Armed - Must be carrying this character's weapon",
    },
    {
        "rarity": "SR",
        "Name": "Reinhard Van Astrea",
        "Source": "Re Zero",
        "Portrait": "/portraits/SR - Reinhard Van Astrea - Re Zero - portrait.png",
        "Thumbnail": "/thumbnails/Reinhard Van Astrea.png",
        "description": "Red hair, white jacket, sword,",
        "conditions": "Armed - Must be carrying this character's weapon",
    },
    {
        "rarity": "SR",
        "Name": "Revenant",
        "Source": "Elden Ring Nightreign",
        "Portrait": "/portraits/SR - Revenant - Eldenring Nightreign - portrait.png",
        "Thumbnail": "/thumbnails/Revenant.png",
        "description": "White hair, white dress, bridal veil, lyre",
        "conditions": "None",
    },
    {
        "rarity": "SR",
        "Name": "Sailor Mars",
        "Source": "Sailor Moon",
        "Portrait": "/portraits/SR - Sailor Mars - Sailor Moon - portrait.png",
        "Thumbnail": "/thumbnails/Sailor Mars.png",
        "description": "Long black hair, red skirt, sailor uniform",
        "conditions": "Solitude - No other Sailor Moon characters are allowed in the photo",
    },
    {
        "rarity": "SR",
        "Name": "Sensei",
        "Source": "Isekai Shikkaku",
        "Portrait": "/portraits/SR - Sensei - Isekai Shikkaku - portrait.png",
        "Thumbnail": "/thumbnails/Sensei.png",
        "description": "Black haori with red trim, messy black hair, carrying a book",
        "conditions": "None",
    },
    {
        "rarity": "SR",
        "Name": "Shinomiya Kagura & Fujiwara Chika",
        "Source": "Kaguya Love is War",
        "Portrait": "/portraits/SR - Shinomiya Kagura & Fujiwara Chika - Kaguya Love is War - portrait.png",
        "Thumbnail": "/thumbnails/Shinomiya Kagura & Fujiwara Chika.png",
        "description": "black hair in ponytail, pink hair with bow, school uniform",
        "conditions": "Duo - Both characters must be in the photo",
    },
    {
        "rarity": "SR",
        "Name": "T.M. Opera O",
        "Source": "Umamusume",
        "Portrait": "/portraits/SR - T.M. Opera O - Umamusume - portrait.png",
        "Thumbnail": "/thumbnails/T.M. Opera O.png",
        "description": "crown, horse ears, tail, cape, pink shorts",
        "conditions": "None",
    },
    {
        "rarity": "SR",
        "Name": "Tamaki Kotatsu",
        "Source": "Fire Force",
        "Portrait": "/portraits/SR - Tamaki Kotatsu- Fire Force - portrait.png",
        "Thumbnail": "/thumbnails/Tamaki Kotatsu.png",
        "description": "Cat ears, fire fighter jacket, bikini top",
        "conditions": "None",
    },
    {
        "rarity": "SR",
        "Name": "Vegapunk 2 Lilith",
        "Source": "One Piece",
        "Portrait": "/portraits/SR - Vegapunk 2 Lilith - One Piece - portrait.png",
        "Thumbnail": "/thumbnails/Vegapunk 2 Lilith.png",
        "description": "Short blonde hair, pink bodysuit, purple jacket",
        "conditions": "None",
    },
    {
        "rarity": "SR",
        "Name": "Yoko Littner",
        "Source": "Gurren Lagan",
        "Portrait": "/portraits/SR - Yoko Littner - Gurren Lagan - portrait.png",
        "Thumbnail": "/thumbnails/Yoko Littner.png",
        "description": "Bikini top, white scarf, red hair",
        "conditions": "None",
    },

    # SSR characters
    {
        "rarity": "SSR",
        "Name": "Biscuit Krueger",
        "Source": "Hunter x Hunter 2011",
        "Portrait": "/portraits/SSR - Biscuit Krueger - Hunter x Hunter 2011 - portrait.png",
        "Thumbnail": "/thumbnails/Biscuit Krueger.png",
        "description": "Long blonde pigtails, pink lolita outfit, might alternatively be a large ripped person",
        "conditions": "None",
    },
    {
        "rarity": "SSR",
        "Name": "Dokugamine Riruku",
        "Source": "Bleach",
        "Portrait": "/portraits/SSR - Dokugamine Riruku - Bleach  - portrait.png",
        "Thumbnail": "/thumbnails/Dokugamine Riruku.png",
        "description": "Pink pigtails, white fur hat, either black/white dress or a onesie",
        "conditions": "None",
    },
    {
        "rarity": "SSR",
        "Name": "Elias Ainsworth",
        "Source": "Ancient Magus Bride",
        "Portrait": "/portraits/SSR - Elias Ainzworth - Ancient Magus Bride - portrait.png",
        "Thumbnail": "/thumbnails/Elias Ainsworth.png",
        "description": "Skull helmet, robes, cane",
        "conditions": "None",
    },
    {
        "rarity": "SSR",
        "Name": "Otoha & Lilisa",
        "Source": "Rock is a Lady's Modesty",
        "Portrait": "/portraits/SSR - Otoha & Lilisa - Rock is a Lady's Modesty - portrait.png",
        "Thumbnail": "/thumbnails/Otoha & Lilisa.png",
        "description": "White school uniforms with a green ribbon, one girl with long purple hair and one with blonde pigtails",
        "conditions": "Duo - Both characters must be in the photo",
    },
    {
        "rarity": "SSR",
        "Name": "Ramlethal Valentine",
        "Source": "Guilty Gear Strive",
        "Portrait": "/portraits/SSR - Ramlethal Valentine - Guilty Gear Strive - portrait.png",
        "Thumbnail": "/thumbnails/Ramlethal Valentine.png",
        "description": "Shoulder length blonde hair, white cape, white uniform, white hat,",
        "conditions": "None",
    },
    {
        "rarity": "SSR",
        "Name": "Ranni the Witch",
        "Source": "Elden Ring",
        "Portrait": "/portraits/SSR - Ranni the Witch - Elden Ring - portrait.png",
        "Thumbnail": "/thumbnails/Ranni the Witch.png",
        "description": "Blue skin, large witch hat, four arms",
        "conditions": "Forearms - Must literally have four arms",
    },
    {
        "rarity": "SSR",
        "Name": "Recluse",
        "Source": "Elden Ring Nightreign",
        "Portrait": "/portraits/SSR - Recluse - Eldenring Nightreign - portrait.png",
        "Thumbnail": "/thumbnails/Recluse.png",
        "description": "White hair, purple robes, floppy witch hat",
        "conditions": "None",
    },
    {
        "rarity": "SSR",
        "Name": "Saotome Ranma",
        "Source": "Ranma One Half",
        "Portrait": "/portraits/SSR - Saotome Ranma - Ranma One Half - portrait.png",
        "Thumbnail": "/thumbnails/Saotome Ranma.png",
        "description": "Matching martial arts outfit, girl with pink hair and boy with black hair",
        "conditions": "Duo - Both characters must be in the photo",
    },
    {
        "rarity": "SSR",
        "Name": "Shidou Ryusei",
        "Source": "Blue Lock",
        "Portrait": "/portraits/SSR - Shidou Ryusei - Blue Lock - portrait.png",
        "Thumbnail": "/thumbnails/Shidou Ryusei.png",
        "description": "Blonde hair with pink tips, soccer uniform",
        "conditions": "None",
    },
    {
        "rarity": "SSR",
        "Name": "Shiki Ryougi",
        "Source": "Fate",
        "Portrait": "/portraits/SSR - Shiki Ryougi - Fate - portrait.png",
        "Thumbnail": "/thumbnails/Shiki Ryougi.png",
        "description": "Blue dress, red jacket, knife",
        "conditions": "None",
    },
    {
        "rarity": "SSR",
        "Name": "Sun Jinwoo Summon",
        "Source": "Solo Leveling",
        "Portrait": "/portraits/SSR - Sun Jinwoo Summon - Solo Leveling - portrait.png",
        "Thumbnail": "/thumbnails/Sun Jinwoo Summon.png",
        "description": "Black armor or body suit, blue or purple trim woven all throughout",
        "conditions": "None",
    },
    {
        "rarity": "SSR",
        "Name": "Ukai Keishin",
        "Source": "Haikyuu!",
        "Portrait": "/portraits/SSR - Ukai Keishin - Haikyuu! - portrait.png",
        "Thumbnail": "/thumbnails/Ukai Keishin.png",
        "description": "Either apron or track suit, blonde hair pulled back",
        "conditions": "None",
    },
    {
        "rarity": "SSR",
        "Name": "Yorishige Suwa",
        "Source": "Elusive Samurai",
        "Portrait": "/portraits/SSR - Yorishige Suwa - Elusive Samurai - portrait.png",
        "Thumbnail": "/thumbnails/Yorishige Suwa.png",
        "description": "Japanese priest robes, tall black hat,",
        "conditions": "None",
    },

    # UR+ characters
    {
        "rarity": "UR+",
        "Name": "Huntrix",
        "Source": "KPOP Demon Slayers",
        "Portrait": "/portraits/UR+ - Huntrix - KPOP Demon Slayers - portrait.png",
        "Thumbnail": "/thumbnails/Huntrix.png",
        "description": "Three girls, pink hair, purple hair, black hair, kpop outfits",
        "conditions": "Trio - All three characters must be in the photo",
    },
    {
        "rarity": "UR+",
        "Name": "Illumi Zoldyck",
        "Source": "Hunter x Hunter 2011",
        "Portrait": "/portraits/UR+ - Illumi Zoldyck - Hunter x Hunter 2011 - portrait.png",
        "Thumbnail": "/thumbnails/Illumi Zoldyck.png",
        "description": "Long black hair, yellow pins, probably green outfit",
        "conditions": "None",
    },
    {
        "rarity": "UR+",
        "Name": "Kessoku Band",
        "Source": "Bocchi the Rock!",
        "Portrait": "/portraits/UR+ - Kessoku Band - Bocchi the Rock! - portrait.png",
        "Thumbnail": "/thumbnails/Kessoku Band.png",
        "description": "4 girls all with different colored hair",
        "conditions": "Quartet - All four band members must be in the photo",
    },
    {
        "rarity": "UR+",
        "Name": "Klaus Von Reinhardt",
        "Source": "Kekkai Sensen",
        "Portrait": "/portraits/UR+ - Klaus Von Reinhardt - Kekkai Sensen - portrait.png",
        "Thumbnail": "/thumbnails/Klaus Von Reinhardt.png",
        "description": "Glasses, strange sideburns, black vest, meant to look like a beastman",
        "conditions": "None",
    },
    {
        "rarity": "UR+",
        "Name": "Midna (True Form)",
        "Source": "The Legend of Zelda Twilight Princess",
        "Portrait": "/portraits/UR+ - Midna (True Form) - The Legend of Zelda Twilight Princess - portrait.png",
        "Thumbnail": "/thumbnails/Midna.png",
        "description": "Blue skin, orange hair, black dress",
        "conditions": "None",
    },
    {
        "rarity": "UR+",
        "Name": "Sinbad",
        "Source": "Magi Labyrinth of Magic",
        "Portrait": "/portraits/UR+ - Sinbad - Magi Labyrinth of Magic - portrait.png",
        "Thumbnail": "/thumbnails/Sinbad.png",
        "description": "Turbin, purple robes, purple hair,",
        "conditions": "None",
    },
]
