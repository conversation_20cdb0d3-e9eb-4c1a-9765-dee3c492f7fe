# Photo Extraction Instructions

This guide will help you safely extract all player-submitted photos from your AWS EC2 bimbo-hunter database to your local system.

## Prerequisites

1. **SSH Access**: You need SSH access to your EC2 instance
2. **SSH Key**: Your private key file (usually a .pem file)
3. **Instance Details**: Your EC2 instance IP address or hostname

## Method 1: Simple Bash Script (Recommended)

The easiest method is to use the provided bash script:

### Step 1: Configure the Script

Edit `extract_photos.sh` and update these variables:

```bash
AWS_HOST="your-ec2-instance-ip-or-hostname"    # Your EC2 public IP or hostname
AWS_USER="ec2-user"                            # Usually 'ec2-user' for Amazon Linux, 'ubuntu' for Ubuntu
AWS_KEY_PATH="~/.ssh/your-key.pem"             # Path to your SSH private key
REMOTE_PROJECT_PATH="/path/to/bimbo-hunter-base"  # Path to your project on EC2
```

### Step 2: Run the Script

```bash
./extract_photos.sh
```

The script will:
- Test your SSH connection
- Check if the remote directory exists
- Count the files to be copied
- Use rsync to safely copy all photos
- Create a local `extracted_photos/user-images/` directory with all photos

## Method 2: Python Script (Advanced)

For more control and metadata extraction, use the Python script:

### Step 1: Configure the Script

Edit `extract_photos_from_aws.py` and update the same variables as above.

### Step 2: Choose a Method

```bash
# Method 1: Rsync (fastest, recommended)
python extract_photos_from_aws.py ssh_rsync

# Method 2: Tar archive (good for slow connections)
python extract_photos_from_aws.py ssh_tar

# Method 3: Database export with metadata (most thorough)
python extract_photos_from_aws.py database_export
```

## Method 3: Manual SSH Commands

If you prefer to do it manually:

```bash
# Test connection
ssh -i ~/.ssh/your-key.pem ec2-user@your-ec2-ip "ls -la /path/to/bimbo-hunter-base/client/public/user-images/"

# Copy files using rsync
rsync -avz --progress -e "ssh -i ~/.ssh/your-key.pem" \
  ec2-user@your-ec2-ip:/path/to/bimbo-hunter-base/client/public/user-images/ \
  ./extracted_photos/user-images/

# Or copy files using scp
scp -i ~/.ssh/your-key.pem -r \
  ec2-user@your-ec2-ip:/path/to/bimbo-hunter-base/client/public/user-images/ \
  ./extracted_photos/
```

## Finding Your Configuration Values

### AWS_HOST
- Go to your EC2 console
- Find your instance
- Copy the "Public IPv4 address" or "Public IPv4 DNS"

### AWS_USER
- Amazon Linux: `ec2-user`
- Ubuntu: `ubuntu`
- CentOS: `centos`

### AWS_KEY_PATH
- This is the .pem file you downloaded when creating the EC2 instance
- Usually stored in `~/.ssh/` directory

### REMOTE_PROJECT_PATH
- This is where you deployed the bimbo-hunter project on EC2
- Common locations: `/home/<USER>/bimbo-hunter-base` or `/opt/bimbo-hunter-base`
- You can find it by SSH'ing in and running: `find / -name "bhunter.db" 2>/dev/null`

## File Structure

After extraction, you'll have:

```
extracted_photos/
└── user-images/
    ├── 28/          # User ID 28
    │   └── 1/       # Board ID 1
    │       ├── 5.jpg
    │       └── 12.png
    ├── 39/          # User ID 39
    │   └── 2/       # Board ID 2
    │       └── 8.jpg
    └── 40/          # User ID 40
        └── 3/       # Board ID 3
            └── 15.png
```

## Safety Notes

- ✅ **Safe**: These methods only READ files, they don't modify anything on the server
- ✅ **Safe**: The database remains untouched
- ✅ **Safe**: Original files stay on the server
- ✅ **Safe**: Uses standard tools (rsync/scp) that are designed for safe file transfer

## Troubleshooting

### "Permission denied (publickey)"
- Check your SSH key path
- Ensure the key has correct permissions: `chmod 600 ~/.ssh/your-key.pem`
- Verify you're using the correct username

### "No such file or directory"
- Check your REMOTE_PROJECT_PATH
- SSH into the server and verify the path exists

### "Connection timeout"
- Check your AWS_HOST (IP address)
- Ensure your EC2 security group allows SSH (port 22) from your IP
- Verify the instance is running

## Next Steps

Once you have the photos locally, you can:
1. Back them up to cloud storage
2. Add them to your local development environment
3. Analyze the data for insights
4. Create a local backup of the database as well

The photos will be organized by user ID and board ID, making it easy to understand which user uploaded which images.
