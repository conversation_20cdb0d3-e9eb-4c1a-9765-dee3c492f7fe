#!/usr/bin/env python3
"""
Script to extract all player submission photos from AWS EC2 to a flat directory.
Each photo gets a random 10-character name to avoid conflicts.
Creates a mapping file to track original paths and metadata.
"""

import os
import sys
import subprocess
import json
import sqlite3
from pathlib import Path
import random
import string

# Configuration
AWS_HOST = "**************"
AWS_USER = "ec2-user"
AWS_KEY_PATH = "bimbo-zone-key.pem"
REMOTE_PROJECT_PATH = "/home/<USER>/bimbo-hunter-base"
LOCAL_EXTRACT_DIR = "./extracted_photos_flat"

def generate_random_filename(extension):
    """Generate a random 10-character filename with the given extension"""
    chars = string.ascii_lowercase + string.digits
    random_name = ''.join(random.choice(chars) for _ in range(10))
    return f"{random_name}.{extension}"

def ensure_local_directory():
    """Create local directory for extracted photos"""
    Path(LOCAL_EXTRACT_DIR).mkdir(exist_ok=True)
    print(f"Created local directory: {LOCAL_EXTRACT_DIR}")

def get_file_list_from_server():
    """Get list of all image files from the server"""
    print("Getting file list from server...")
    
    # Get all files with their full paths
    find_cmd = [
        "ssh", "-i", AWS_KEY_PATH, f"{AWS_USER}@{AWS_HOST}",
        f"find {REMOTE_PROJECT_PATH}/client/public/user-images -type f"
    ]
    
    try:
        result = subprocess.run(find_cmd, check=True, capture_output=True, text=True)
        file_paths = result.stdout.strip().split('\n')
        file_paths = [path for path in file_paths if path.strip()]  # Remove empty lines
        print(f"Found {len(file_paths)} files on server")
        return file_paths
    except subprocess.CalledProcessError as e:
        print(f"Failed to get file list: {e}")
        return []

def download_and_rename_files(file_paths):
    """Download files and rename them with random names"""
    ensure_local_directory()
    
    mapping = {}
    success_count = 0
    used_names = set()
    
    for i, remote_path in enumerate(file_paths):
        print(f"Processing {i+1}/{len(file_paths)}: {remote_path}")
        
        # Extract original filename and extension
        original_filename = os.path.basename(remote_path)
        if '.' in original_filename:
            extension = original_filename.rsplit('.', 1)[1].lower()
        else:
            extension = 'jpg'  # Default extension
        
        # Generate unique random filename
        while True:
            new_filename = generate_random_filename(extension)
            if new_filename not in used_names:
                used_names.add(new_filename)
                break
        
        local_path = os.path.join(LOCAL_EXTRACT_DIR, new_filename)
        
        # Download file using scp
        scp_cmd = [
            "scp", "-i", AWS_KEY_PATH,
            f"{AWS_USER}@{AWS_HOST}:{remote_path}",
            local_path
        ]
        
        try:
            subprocess.run(scp_cmd, check=True, capture_output=True)
            success_count += 1
            
            # Parse the path to extract user_id, board_id, and square_index
            path_parts = remote_path.split('/')
            user_id = None
            board_id = None
            square_index = None
            
            # Find user-images in the path and extract info
            try:
                user_images_idx = path_parts.index('user-images')
                if len(path_parts) > user_images_idx + 3:
                    user_id = path_parts[user_images_idx + 1]
                    board_id = path_parts[user_images_idx + 2]
                    square_filename = path_parts[user_images_idx + 3]
                    square_index = square_filename.split('.')[0]  # Remove extension
            except (ValueError, IndexError):
                pass
            
            # Store mapping information
            mapping[new_filename] = {
                'original_path': remote_path,
                'original_filename': original_filename,
                'user_id': user_id,
                'board_id': board_id,
                'square_index': square_index,
                'extension': extension
            }
            
            print(f"  ✓ Downloaded as: {new_filename}")
            
        except subprocess.CalledProcessError as e:
            print(f"  ✗ Failed to download: {remote_path}")
    
    return mapping, success_count

def save_mapping_file(mapping):
    """Save the filename mapping to a JSON file"""
    mapping_file = os.path.join(LOCAL_EXTRACT_DIR, 'filename_mapping.json')
    with open(mapping_file, 'w') as f:
        json.dump(mapping, f, indent=2)
    print(f"Mapping file saved: {mapping_file}")

def get_database_metadata():
    """Download and analyze the database for additional metadata"""
    print("Downloading database for metadata...")
    
    db_local_path = os.path.join(LOCAL_EXTRACT_DIR, 'bhunter.db')
    scp_db_cmd = [
        "scp", "-i", AWS_KEY_PATH,
        f"{AWS_USER}@{AWS_HOST}:{REMOTE_PROJECT_PATH}/bhunter.db",
        db_local_path
    ]
    
    try:
        subprocess.run(scp_db_cmd, check=True, capture_output=True)
        print("Database downloaded successfully!")
        
        # Extract user information
        conn = sqlite3.connect(db_local_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Get user information
        cursor.execute("SELECT id, display_name, created_at FROM users")
        users = {str(row['id']): dict(row) for row in cursor.fetchall()}
        
        # Get progress information with user images
        cursor.execute("""
            SELECT p.user_id, p.board_id, p.user_images, p.score, p.updated_at, u.display_name
            FROM progress p
            JOIN users u ON p.user_id = u.id
            WHERE p.user_images != '{}' AND p.user_images IS NOT NULL
        """)
        
        progress_data = []
        for row in cursor.fetchall():
            progress_data.append(dict(row))
        
        conn.close()
        
        # Save metadata
        metadata = {
            'users': users,
            'progress': progress_data,
            'extraction_info': {
                'total_users': len(users),
                'users_with_uploads': len(progress_data),
                'extraction_date': str(subprocess.run(['date'], capture_output=True, text=True).stdout.strip())
            }
        }
        
        metadata_file = os.path.join(LOCAL_EXTRACT_DIR, 'database_metadata.json')
        with open(metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2)
        print(f"Database metadata saved: {metadata_file}")
        
        return metadata
        
    except subprocess.CalledProcessError as e:
        print(f"Failed to download database: {e}")
        return None

def main():
    print("=== Bimbo Hunter Photo Extraction (Flat Directory) ===")
    print(f"Extracting photos to: {LOCAL_EXTRACT_DIR}")
    print(f"Server: {AWS_HOST}")
    print()
    
    # Test SSH connection
    print("Testing SSH connection...")
    test_cmd = [
        "ssh", "-i", AWS_KEY_PATH, "-o", "ConnectTimeout=10",
        f"{AWS_USER}@{AWS_HOST}", "echo 'Connection successful'"
    ]
    
    try:
        subprocess.run(test_cmd, check=True, capture_output=True)
        print("✓ SSH connection successful")
    except subprocess.CalledProcessError:
        print("✗ SSH connection failed")
        return 1
    
    # Get file list from server
    file_paths = get_file_list_from_server()
    if not file_paths:
        print("No files found or failed to get file list")
        return 1
    
    # Download and rename files
    print(f"\nDownloading {len(file_paths)} files...")
    mapping, success_count = download_and_rename_files(file_paths)
    
    # Save mapping file
    save_mapping_file(mapping)
    
    # Get database metadata
    get_database_metadata()
    
    # Summary
    print(f"\n=== Extraction Complete ===")
    print(f"Total files found: {len(file_paths)}")
    print(f"Successfully downloaded: {success_count}")
    print(f"Failed downloads: {len(file_paths) - success_count}")
    print(f"Files saved to: {LOCAL_EXTRACT_DIR}")
    print(f"Mapping file: {LOCAL_EXTRACT_DIR}/filename_mapping.json")
    print(f"Database metadata: {LOCAL_EXTRACT_DIR}/database_metadata.json")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
