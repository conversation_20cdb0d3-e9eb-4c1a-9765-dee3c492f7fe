#!/usr/bin/env python3
"""
Database Analysis Script for Bimbo Hunter Game

This script analyzes the game database to extract various statistics about
character claims, user activity, and gameplay patterns.
"""

import sqlite3
import json
import sys
import os
from datetime import datetime
from collections import defaultdict, Counter
from config import DB_FILE
import characters

def connect_to_database():
    """Connect to the local database."""
    if not os.path.exists(DB_FILE):
        print(f"❌ Database file '{DB_FILE}' not found!")
        print("Make sure you're running this from the correct directory.")
        return None
    
    try:
        conn = sqlite3.connect(DB_FILE)
        conn.row_factory = sqlite3.Row
        return conn
    except sqlite3.Error as e:
        print(f"❌ Error connecting to database: {e}")
        return None

def get_character_by_name(name):
    """Get character data by name."""
    for char in characters.characters:
        if char['Name'] == name:
            return char
    return None

def analyze_most_claimed_by_rarity(conn):
    """Analyze most claimed characters by rarity (excluding FREE)."""
    print("🎯 MOST CLAIMED CHARACTERS BY RARITY (excluding FREE)")
    print("=" * 60)
    
    cursor = conn.cursor()
    
    # Get all progress data
    cursor.execute("""
        SELECT p.board_id, p.marked_cells, b.board_data, u.display_name
        FROM progress p
        JOIN boards b ON p.board_id = b.id
        JOIN users u ON p.user_id = u.id
    """)
    
    progress_data = cursor.fetchall()
    
    # Count claims by character and rarity
    rarity_claims = defaultdict(lambda: defaultdict(int))
    total_claims = defaultdict(int)
    
    for row in progress_data:
        board_data = json.loads(row['board_data'])
        marked_cells = json.loads(row['marked_cells'])
        
        for cell_index in marked_cells:
            if 0 <= cell_index < len(board_data):
                char = board_data[cell_index]
                char_name = char['Name']
                char_rarity = char['rarity']
                
                if char_rarity != 'FREE':  # Exclude FREE tier
                    rarity_claims[char_rarity][char_name] += 1
                    total_claims[char_name] += 1
    
    # Display results by rarity
    for rarity in ['R', 'SR', 'SSR', 'UR+']:
        if rarity in rarity_claims:
            print(f"\n📊 {rarity} Characters:")
            sorted_chars = sorted(rarity_claims[rarity].items(), key=lambda x: x[1], reverse=True)
            for char_name, count in sorted_chars[:5]:  # Top 5
                print(f"   {char_name}: {count} claims")
    
    return total_claims

def analyze_most_claimed_overall(total_claims):
    """Analyze the most claimed character overall."""
    print("\n🏆 MOST CLAIMED CHARACTER OVERALL")
    print("=" * 40)
    
    if total_claims:
        most_claimed = max(total_claims.items(), key=lambda x: x[1])
        char_name, count = most_claimed
        char_data = get_character_by_name(char_name)
        rarity = char_data['rarity'] if char_data else 'Unknown'
        
        print(f"🥇 {char_name} ({rarity}): {count} claims")
        
        # Show top 10
        print(f"\n📈 Top 10 Most Claimed Characters:")
        sorted_claims = sorted(total_claims.items(), key=lambda x: x[1], reverse=True)
        for i, (char_name, count) in enumerate(sorted_claims[:10], 1):
            char_data = get_character_by_name(char_name)
            rarity = char_data['rarity'] if char_data else 'Unknown'
            print(f"   {i:2d}. {char_name} ({rarity}): {count} claims")
    else:
        print("No claims found in database.")

def check_timestamps(conn):
    """Check if timestamps are available and analyze time-based data."""
    print("\n⏰ TIMESTAMP ANALYSIS")
    print("=" * 30)
    
    cursor = conn.cursor()
    
    # Check if we have timestamp data
    cursor.execute("SELECT updated_at FROM progress WHERE updated_at IS NOT NULL LIMIT 1")
    has_timestamps = cursor.fetchone() is not None
    
    if not has_timestamps:
        print("❌ No timestamp data found in the database.")
        print("   The following analyses cannot be performed:")
        print("   - Fastest back-to-back claims")
        print("   - Daily claim counts")
        print("   - User comeback analysis")
        print("   - User stuck analysis")
        return False
    
    print("✅ Timestamp data found! Performing time-based analysis...")
    return True

def analyze_daily_claims(conn):
    """Analyze claims per day."""
    print("\n📅 DAILY CLAIM ANALYSIS")
    print("=" * 30)
    
    cursor = conn.cursor()
    cursor.execute("""
        SELECT DATE(updated_at) as claim_date, COUNT(*) as claims_count
        FROM progress
        WHERE updated_at IS NOT NULL
        GROUP BY DATE(updated_at)
        ORDER BY claim_date
    """)
    
    daily_data = cursor.fetchall()
    
    if daily_data:
        print("Claims per day:")
        for row in daily_data:
            print(f"   {row['claim_date']}: {row['claims_count']} claims")
    else:
        print("No daily data available.")

def analyze_user_patterns(conn):
    """Analyze user gameplay patterns."""
    print("\n👥 USER GAMEPLAY PATTERNS")
    print("=" * 35)
    
    cursor = conn.cursor()
    
    # Get user progress over time
    cursor.execute("""
        SELECT u.display_name, p.score, p.updated_at, p.marked_cells
        FROM progress p
        JOIN users u ON p.user_id = u.id
        ORDER BY u.display_name, p.updated_at
    """)
    
    user_data = cursor.fetchall()
    
    if not user_data:
        print("No user progress data found.")
        return
    
    # Analyze user scores and progress
    user_scores = {}
    for row in user_data:
        username = row['display_name']
        score = row['score']
        marked_cells = json.loads(row['marked_cells'])
        
        if username not in user_scores:
            user_scores[username] = {
                'score': score,
                'squares_claimed': len(marked_cells)
            }
        else:
            # Keep the latest/highest score
            if score > user_scores[username]['score']:
                user_scores[username] = {
                    'score': score,
                    'squares_claimed': len(marked_cells)
                }
    
    print("Current user standings:")
    sorted_users = sorted(user_scores.items(), key=lambda x: x[1]['score'], reverse=True)
    for i, (username, data) in enumerate(sorted_users, 1):
        print(f"   {i:2d}. {username}: {data['score']} points ({data['squares_claimed']} squares)")

def main():
    """Main analysis function."""
    print("🎮 BIMBO HUNTER DATABASE ANALYSIS")
    print("=" * 50)
    
    # Connect to database
    conn = connect_to_database()
    if not conn:
        sys.exit(1)
    
    try:
        # Analyze most claimed characters by rarity
        total_claims = analyze_most_claimed_by_rarity(conn)
        
        # Analyze most claimed character overall
        analyze_most_claimed_overall(total_claims)
        
        # Check for timestamps and perform time-based analysis
        has_timestamps = check_timestamps(conn)
        
        if has_timestamps:
            analyze_daily_claims(conn)
            analyze_user_patterns(conn)
        else:
            # Still show current user patterns without time analysis
            analyze_user_patterns(conn)
        
        print("\n✅ Analysis complete!")
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        sys.exit(1)
    finally:
        conn.close()

if __name__ == "__main__":
    main()
